package whatsapp_broadcast

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/errors"
)

type UpdateWhatsappBroadcastRequest struct {
	base.RequestPathId
	Name       string `json:"name"`
	WorkflowId uint   `json:"workflow_id"`
	StartAfter int64  `json:"start_after"`
	Status     string `json:"status"`
}

func (r *UpdateWhatsappBroadcastRequest) ToEntity(broadcast *entities.WhatsappBroadcast) *entities.WhatsappBroadcast {
	broadcast.Name.Set(r.Name)
	broadcast.WorkflowId.Set(r.WorkflowId)
	broadcast.StartAfter.Set(r.StartAfter)
	broadcast.Status.Set(r.Status)
	return broadcast
}

var ApiWhatsappBroadcastUpdate = metagin.Put[UpdateWhatsappBroadcastRequest, entities.WhatsappBroadcastDTO]("updateWhatsappBroadcast").
	Route("/whatsapp/broadcast/:id").
	Handler(func(ctx metagin.IContext[UpdateWhatsappBroadcastRequest, entities.WhatsappBroadcastDTO]) {
		broadcast, err := new(entities.WhatsappBroadcast).Repo(ctx.DB(), ctx.WorkspaceId()).FindOne(ctx.DB().Eq("id", ctx.Request().ID))
		if err != nil {
			ctx.Error(err)
			return
		}
		if broadcast == nil {
			ctx.Error(errors.ErrWhatsappBroadcastNotFound)
			return
		}

		if broadcast.Status.Get() != configs.WhatsappBroadcastStatusCompleted && broadcast.Status.Get() != configs.WhatsappBroadcastStatusCancelled {
			broadcast.Status.Set(ctx.Request().Status)
			if broadcast.Status.Get() == configs.WhatsappBroadcastStatusCancelled {
				jobs, err := new(entities.WhatsappBroadcastJob).Repo(ctx.DB(), ctx.WorkspaceId()).FindAll(ctx.DB().And(
					ctx.DB().Eq("broadcast_id", broadcast.ID),
					ctx.DB().Eq("status", configs.WhatsappBroadcastJobStatusPending),
				))
				if err != nil {
					ctx.Error(err)
					return
				}
				for i := range jobs {
					jobs[i].Status.Set(configs.WhatsappBroadcastJobStatusCancelled)
				}
				if len(jobs) > 0 {
					new(entities.WhatsappBroadcastJob).Repo(ctx.DB(), ctx.WorkspaceId()).SaveAll(jobs)
				}
			}
			broadcast.StartAfter.Set(ctx.Request().StartAfter)
		}

		broadcast = ctx.Request().ToEntity(broadcast)
		broadcast, err = broadcast.Repo(ctx.DB(), ctx.WorkspaceId()).Save(broadcast)
		if err != nil {
			ctx.Error(err)
			return
		}
		ctx.OK(broadcast.DTO())
	}).Build()

package whatsapp_broadcast

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/errors"
)

type BroadcastDTO struct {
	entities.WhatsappBroadcastDTO

	FailedCount  int64 `json:"failed_count"`
	SentCount    int64 `json:"sent_count"`
	PendingCount int64 `json:"pending_count"`
}

var ApiWhatsappBroadcastGet = metagin.Get[base.RequestPathId, BroadcastDTO]("getWhatsappBroadcast").
	Route("/whatsapp/broadcast/:id").
	Handler(func(ctx metagin.IContext[base.RequestPathId, BroadcastDTO]) {

		broadcast, err := new(entities.WhatsappBroadcast).Repo(ctx.DB().Preload("Channel", "Template", "Workflow"), ctx.WorkspaceId()).FindOne(ctx.DB().Eq("id", ctx.Request().ID))
		if err != nil {
			ctx.Error(err)
			return
		}
		if broadcast == nil {
			ctx.Error(errors.ErrWhatsappBroadcastNotFound)
			return
		}

		var failed, sent, pending int64

		failed, err = new(entities.WhatsappBroadcastJob).Repo(
			ctx.DB(), ctx.WorkspaceId()).Count(ctx.DB().And(
			ctx.DB().Eq("status", configs.WhatsappBroadcastJobStatusFailed),
			ctx.DB().Eq("broadcast_id", broadcast.ID),
		))
		if err != nil {
			ctx.Error(err)
			return
		}

		sent, err = new(entities.WhatsappBroadcastJob).Repo(
			ctx.DB(), ctx.WorkspaceId()).Count(ctx.DB().And(
			ctx.DB().Eq("status", configs.WhatsappBroadcastJobStatusSent),
			ctx.DB().Eq("broadcast_id", broadcast.ID),
		))
		if err != nil {
			ctx.Error(err)
			return
		}

		pending, err = new(entities.WhatsappBroadcastJob).Repo(
			ctx.DB(), ctx.WorkspaceId()).Count(ctx.DB().And(
			ctx.DB().Or(
				ctx.DB().Eq("status", configs.WhatsappBroadcastJobStatusPending),
				ctx.DB().Eq("status", configs.WhatsappBroadcastStatusSending),
			),
			ctx.DB().Eq("broadcast_id", broadcast.ID),
		))
		if err != nil {
			ctx.Error(err)
			return
		}

		ctx.OK(&BroadcastDTO{
			WhatsappBroadcastDTO: *broadcast.DTO(),
			FailedCount:          failed,
			SentCount:            sent,
			PendingCount:         pending,
		})
	}).Build()

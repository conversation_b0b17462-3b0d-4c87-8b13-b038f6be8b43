package whatsapp_broadcast

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_chat/configs"
	"github.com/metadiv-tech/mod_chat/entities"
)

var ApiWhatsappBroadcastList = metagin.Get[base.RequestListing, []BroadcastDTO]("getWhatsappBroadcastList").
	Route("/whatsapp/broadcast").
	Handler(func(ctx metagin.IContext[base.RequestListing, []BroadcastDTO]) {
		broadcasts, page, err := new(entities.WhatsappBroadcast).Repo(ctx.DB().Preload("Channel", "Template", "Workflow"), ctx.WorkspaceId()).FindAllComplex(
			&ctx.Request().Pagination,
			&ctx.Request().Sorting,
			nil,
		)
		if err != nil {
			ctx.Error(err)
			return
		}
		ds := make([]BroadcastDTO, 0)
		for _, broadcast := range broadcasts {

			failed, err := new(entities.WhatsappBroadcastJob).Repo(
				ctx.DB(), ctx.WorkspaceId()).Count(ctx.DB().And(
				ctx.DB().Eq("status", configs.WhatsappBroadcastJobStatusFailed),
				ctx.DB().Eq("broadcast_id", broadcast.ID),
			))
			if err != nil {
				ctx.Error(err)
				return
			}
			sent, err := new(entities.WhatsappBroadcastJob).Repo(
				ctx.DB(), ctx.WorkspaceId()).Count(ctx.DB().And(
				ctx.DB().Eq("status", configs.WhatsappBroadcastJobStatusSent),
				ctx.DB().Eq("broadcast_id", broadcast.ID),
			))
			if err != nil {
				ctx.Error(err)
				return
			}
			pending, err := new(entities.WhatsappBroadcastJob).Repo(
				ctx.DB(), ctx.WorkspaceId()).Count(ctx.DB().And(
				ctx.DB().Or(
					ctx.DB().Eq("status", configs.WhatsappBroadcastJobStatusPending),
					ctx.DB().Eq("status", configs.WhatsappBroadcastStatusSending),
				),
				ctx.DB().Eq("broadcast_id", broadcast.ID),
			))
			if err != nil {
				ctx.Error(err)
				return
			}
			ds = append(ds, BroadcastDTO{
				WhatsappBroadcastDTO: *broadcast.DTO(),
				FailedCount:          failed,
				SentCount:            sent,
				PendingCount:         pending,
			})
		}

		ctx.OK(&ds, page)
	}).Build()

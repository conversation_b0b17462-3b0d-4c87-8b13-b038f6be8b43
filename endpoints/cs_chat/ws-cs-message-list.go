package cs_chat

import (
	"time"

	"github.com/gorilla/websocket"
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_chat/entities"
	"github.com/metadiv-tech/mod_chat/services/cs_chat_service"
)

type CsMessageListMessage struct {
	Type string                  `json:"type"`
	Data []entities.CsMessageDTO `json:"data,omitempty"`
	Page *metaorm.Pagination     `json:"page,omitempty"`
}

type CsMessageListRequest struct {
	metaorm.Sorting
	metaorm.Pagination
	ConversationId uint `form:"conversation_id" json:"conversation_id"`
}

// Track last message state for change detection
type LastMessageState struct {
	ID     uint     `json:"id"`
	Status string   `json:"status"`
	Emojis []string `json:"emojis"`
}

var WsCsMessageList = metagin.WebSocket[CsMessageListRequest, CsMessageListMessage]("csMessageList").
	Route("/cs/message").
	Handler(func(ctx metagin.IAuthWebSocketContext, ws *websocket.Conn) {
		ctx.LogInfo("CS message list WebSocket connection established", "userID", ctx.UserId(), "workspaceID", ctx.WorkspaceId())

		messageManager := cs_chat_service.NewMessageManager(ctx.DB())

		// Parse query parameters for initial filters using framework binding
		var initialReq CsMessageListRequest
		if err := ctx.GinContext().ShouldBindQuery(&initialReq); err != nil {
			ctx.LogError("Failed to bind query parameters", "error", err)
		}

		// Helper function to get last message state
		getLastMessageState := func(conversationId uint) *LastMessageState {
			if conversationId == 0 {
				return nil
			}

			// Query to get the last CsMessage for this conversation (most recent by created_at)
			andBuilder := metaorm.NewAndQBuilder()
			andBuilder.Add(metaorm.Eq("conversation_id", conversationId))

			var csMessage entities.CsMessage
			msgs, _, err := csMessage.Repo(ctx.DB().Preload("Message").Preload("Broadcast"), ctx.WorkspaceId()).FindAllComplex(
				metaorm.Paginate(1, 1), metaorm.Sort("created_at", false), andBuilder.Build())
			if err != nil {
				ctx.LogDebug("No messages found or error fetching last message", "error", err, "conversationId", conversationId)
				return nil
			}
			if len(msgs) == 0 {
				return nil
			}

			csMessage = msgs[0]

			// Extract status and emojis from the related Message
			status := ""
			var emojis []string
			if csMessage.Message != nil {
				status = csMessage.Message.Status.Get()
				emojis = csMessage.Message.Emojis.Get()
			}

			return &LastMessageState{
				ID:     csMessage.ID,
				Status: status,
				Emojis: emojis,
			}
		}

		// Send message list function
		sendMessageList := func(req *CsMessageListRequest) {
			listReq := &cs_chat_service.ListMessagesRequest{
				ConversationId: req.ConversationId,
			}

			messages, page, err := messageManager.ListMessages(
				listReq,
				ctx.IsAdmin(),
				ctx.UserId(),
				ctx.WorkspaceId(),
				&req.Sorting,
				&req.Pagination,
			)
			if err != nil {
				ctx.LogError("Failed to fetch messages", "error", err)
				response := CsMessageListMessage{
					Type: "error",
					Data: nil,
				}
				if err := ws.WriteJSON(response); err != nil {
					ctx.LogError("Failed to send error message", "error", err)
				}
				return
			}

			response := CsMessageListMessage{
				Type: "list",
				Data: messages,
				Page: page,
			}

			if err := ws.WriteJSON(response); err != nil {
				ctx.LogError("Failed to send message list", "error", err)
			}
		}

		// Send initial list with query parameter filters
		sendMessageList(&initialReq)

		// Track last known message state for the conversation
		var lastMessageState *LastMessageState = nil
		if initialReq.ConversationId != 0 {
			// Get the last message state
			lastMessageState = getLastMessageState(initialReq.ConversationId)
		}

		// Create a ticker for polling every 3 seconds
		ticker := time.NewTicker(3 * time.Second)
		defer ticker.Stop()

		// Create a done channel to signal when to stop
		done := make(chan bool)

		// Start monitoring for message changes in a goroutine
		go func() {
			for {
				select {
				case <-ticker.C:
					// Only check if we have a conversation ID
					if initialReq.ConversationId != 0 {
						// Get current last message state
						currentState := getLastMessageState(initialReq.ConversationId)

						// Check if state has changed
						stateChanged := false
						if lastMessageState == nil && currentState != nil {
							// New message appeared
							stateChanged = true
						} else if lastMessageState != nil && currentState == nil {
							// All messages were deleted (edge case)
							stateChanged = true
						} else if lastMessageState != nil && currentState != nil {
							// Compare states
							if lastMessageState.ID != currentState.ID ||
								lastMessageState.Status != currentState.Status ||
								!stringSlicesEqual(lastMessageState.Emojis, currentState.Emojis) {
								stateChanged = true
							}
						}

						// If state has changed, send updated message list
						if stateChanged {
							ctx.LogDebug("Message state changed", "oldState", lastMessageState, "newState", currentState)
							sendMessageList(&initialReq)
							lastMessageState = currentState
						}
					}
				case <-done:
					return
				}
			}
		}()

		// Listen for WebSocket disconnection or errors
		for {
			var req CsMessageListRequest
			if err := ws.ReadJSON(&req); err != nil {
				ctx.LogError("WebSocket connection closed", "error", err)
				break
			}

			// Handle frontend requests with updated parameters
			if req.ConversationId != 0 {
				// Check if conversation changed
				if req.ConversationId != initialReq.ConversationId {
					ctx.LogDebug("Conversation changed, updating monitoring", "oldConversation", initialReq.ConversationId, "newConversation", req.ConversationId)
					initialReq = req

					// Reset message state for new conversation
					lastMessageState = getLastMessageState(initialReq.ConversationId)
				} else {
					// Same conversation, but update pagination/sorting parameters
					ctx.LogDebug("Updating pagination/sorting parameters", "conversationId", req.ConversationId)
					initialReq.Pagination = req.Pagination
					initialReq.Sorting = req.Sorting
				}

				// Send immediate update with new parameters
				sendMessageList(&initialReq)
			}
		}

		// Signal the monitoring goroutine to stop
		done <- true

		ctx.LogInfo("CS message list WebSocket connection closed", "userID", ctx.UserId())
	}).Build()

// Helper function to compare string slices
func stringSlicesEqual(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i, v := range a {
		if v != b[i] {
			return false
		}
	}
	return true
}
